package com.example.recoverdev.ui.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.example.recoverdev.data.model.FileType
import com.example.recoverdev.data.model.RecoverableFile
import com.example.recoverdev.ui.components.FileItem
import com.example.recoverdev.ui.components.GridFileItem
import com.example.recoverdev.viewmodel.RecoveredDataViewModel
import java.text.SimpleDateFormat
import java.util.*
import com.example.recoverdev.R

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RecoveredDataScreen(
    onFileClick: (RecoverableFile) -> Unit,
    viewModel: RecoveredDataViewModel
) {
    val selectedTab by viewModel.selectedTab.collectAsState()
    val recoveredFiles by viewModel.recoveredFiles.collectAsState()
    val isSelectionMode by viewModel.isSelectionMode.collectAsState()
    val selectedFiles by viewModel.selectedFiles.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.refreshRecoveredFiles()
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
    ) {

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 12.dp)
                .background(
                    Color(0xFFACA6C3).copy(alpha = 0.39f),
                    RoundedCornerShape(12.dp)
                ),
        ) {
            FileType.values().forEach { fileType ->
                if (selectedTab == fileType) {
                    Card(
                        modifier = Modifier
                            .weight(1f)
                            .clickable { viewModel.selectTab(fileType) }
                            .zIndex(2f),
                        shape = RoundedCornerShape(8.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = Color.White
                        ),
                        elevation = CardDefaults.cardElevation(
                            defaultElevation = 6.dp
                        )
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 6.dp, horizontal = 4.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = when (fileType) {
                                    FileType.PHOTO -> stringResource(R.string.photos)
                                    FileType.VIDEO -> stringResource(R.string.videos)
                                    FileType.AUDIO -> stringResource(R.string.audios)
                                    FileType.OTHER -> stringResource(R.string.other_files)
                                },
                                fontSize = 11.sp,
                                fontWeight = FontWeight.Medium,
                                color = colorResource(R.color.first_text_black)
                            )
                        }
                    }
                } else {
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .clickable { viewModel.selectTab(fileType) }
                            .padding(vertical = 6.dp, horizontal = 4.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = when (fileType) {
                                FileType.PHOTO -> stringResource(R.string.photos)
                                FileType.VIDEO -> stringResource(R.string.videos)
                                FileType.AUDIO -> stringResource(R.string.audios)
                                FileType.OTHER -> stringResource(R.string.other_files)
                            },
                            fontSize = 11.sp,
                            fontWeight = FontWeight.Medium,
                            color = colorResource(R.color.first_text_black)
                        )
                    }
                }
            }
        }
        
        // File list content with consistent styling as RecoveryScreen
        val filteredFiles = recoveredFiles.filter { it.type == selectedTab }
        
        if (filteredFiles.isEmpty()) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Image(
                        painter = painterResource(
                            id = when (selectedTab) {
                                FileType.PHOTO -> R.mipmap.no_image
                                FileType.VIDEO -> R.mipmap.no_video
                                FileType.AUDIO -> R.mipmap.no_audio
                                FileType.OTHER -> R.mipmap.no_file
                            }
                        ),
                        contentDescription = "No files",
                        modifier = Modifier.size(160.dp)
                    )
                    Text(
                        text = stringResource(R.string.no_recoverable_files),
                        fontSize = 16.sp,
                        color = Color.Gray
                    )
                }
            }
        } else {
            // Sort files by date (newest first) and group by date
            val sortedFiles = filteredFiles.sortedByDescending { it.dateModified }
            val groupedFiles = sortedFiles.groupBy { file ->
                val date = Date(file.dateModified)
                SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(date)
            }
            
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Display files grouped by date
                groupedFiles.forEach { (date, filesInDate) ->
                    item {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = date,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color.Black
                            )
                        }
                    }

                    if (selectedTab == FileType.PHOTO || selectedTab == FileType.VIDEO || selectedTab == FileType.AUDIO) {
                        val chunkedFiles = filesInDate.chunked(4)
                        items(chunkedFiles) { rowFiles ->
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                rowFiles.forEach { file ->
                                    GridFileItem(
                                        file = file,
                                        isSelected = selectedFiles.contains(file.id),
                                        isSelectionMode = isSelectionMode,
                                        onClick = { 
                                            if (isSelectionMode) {
                                                viewModel.toggleFileSelection(file.id)
                                            } else {
                                                onFileClick(file)
                                            }
                                        },
                                        modifier = Modifier.weight(1f)
                                    )
                                }
                                // Fill remaining spaces if row is not complete
                                repeat(4 - rowFiles.size) {
                                    Spacer(modifier = Modifier.weight(1f))
                                }
                            }
                        }
                    } else {
                        // List layout for other files
                        items(filesInDate) { file ->
                            FileItem(
                                file = file,
                                isSelected = selectedFiles.contains(file.id),
                                isSelectionMode = isSelectionMode,
                                onClick = { 
                                    if (isSelectionMode) {
                                        viewModel.toggleFileSelection(file.id)
                                    } else {
                                        onFileClick(file)
                                    }
                                },
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun RecoveredDataScreenContent(
    onFileClick: (RecoverableFile) -> Unit,
    viewModel: RecoveredDataViewModel
) {
    RecoveredDataScreen(
        onFileClick = onFileClick,
        viewModel = viewModel
    )
}

