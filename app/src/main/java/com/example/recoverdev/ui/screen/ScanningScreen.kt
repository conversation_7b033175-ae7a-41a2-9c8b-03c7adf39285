package com.example.recoverdev.ui.screen

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.lottie.compose.*
import com.example.recoverdev.R
import kotlinx.coroutines.delay

enum class ScanType {
    PHOTO, VIDEO, AUDIO, DOCUMENT, ALL
}

// Helper function to convert FileType to ScanType
fun fileTypeToScanType(fileType: com.example.recoverdev.data.model.FileType): ScanType {
    return when (fileType) {
        com.example.recoverdev.data.model.FileType.PHOTO -> ScanType.PHOTO
        com.example.recoverdev.data.model.FileType.VIDEO -> ScanType.VIDEO
        com.example.recoverdev.data.model.FileType.AUDIO -> ScanType.AUDIO
        com.example.recoverdev.data.model.FileType.OTHER -> ScanType.DOCUMENT
    }
}

@Composable
fun ScanningScreen(
    scanType: ScanType = ScanType.ALL,
    onScanComplete: () -> Unit,
    onStartScan: () -> Unit = {},
    onBackPressed: () -> Unit = {},
    currentScanPath: String = "",
    scanProgress: Float = 0f,
    isActuallyScanning: Boolean = false
) {
    // Use scanType as key to ensure fresh state for each scan type
    val screenKey = remember(scanType) { System.currentTimeMillis() }

    var isScanning by remember(screenKey) { mutableStateOf(false) }
    var progress by remember(screenKey) { mutableStateOf(0f) }
    var scanningText by remember(screenKey) { mutableStateOf("") }

    android.util.Log.d("ScanningScreen", "ScanningScreen created with key: $screenKey, scanType: $scanType")

    // Ensure state is reset when component is disposed
    DisposableEffect(screenKey) {
        onDispose {
            android.util.Log.d("ScanningScreen", "Component disposed for key: $screenKey")
        }
    }

    val actualProgress = if (isActuallyScanning) scanProgress else progress
    val actualScanPath = if (isActuallyScanning && currentScanPath.isNotEmpty()) currentScanPath else ""

    // Only block back navigation during actual scanning, not during UI animation
    BackHandler(enabled = isActuallyScanning) {
        // During actual scanning, we don't allow back navigation
        // User must wait for scan to complete or fail
    }

    // Handle back navigation when not actually scanning
    BackHandler(enabled = isScanning && !isActuallyScanning) {
        // Allow canceling the UI animation and returning
        isScanning = false
        progress = 0f
        scanningText = ""
        onBackPressed()
    }
    
    // Get string resources
    val initializingScan = stringResource(R.string.initializing_scan)
    val scanningStorageDevices = stringResource(R.string.scanning_storage_devices)
    val analyzingFileSystem = stringResource(R.string.analyzing_file_system)
    val detectingDeletedFiles = stringResource(R.string.detecting_deleted_files)
    val verifyingFileIntegrity = stringResource(R.string.verifying_file_integrity)
    val scanAlmostComplete = stringResource(R.string.scan_almost_complete)
    

    
    // Get Lottie animation based on scan type
    val animationAsset = when (scanType) {
        ScanType.PHOTO -> "anim/home_photos.json"
        ScanType.VIDEO -> "anim/home_video.json"
        ScanType.AUDIO -> "anim/home_audio.json"
        ScanType.DOCUMENT -> "anim/home_other file.json"
        ScanType.ALL -> "anim/normal_scan.json"
    }
    
    // Lottie composition
    val composition by rememberLottieComposition(LottieCompositionSpec.Asset(animationAsset))
    

    
    // Scan animation effect - only runs when user manually starts scanning
    LaunchedEffect(screenKey, isScanning) {
        if (isScanning && !isActuallyScanning) {
            android.util.Log.d("ScanningScreen", "User clicked scan button for key: $screenKey, starting animation")

            val scanningSteps = listOf(
                Pair(initializingScan, 0.2f),
                Pair(scanningStorageDevices, 0.4f),
                Pair(analyzingFileSystem, 0.6f),
                Pair(detectingDeletedFiles, 0.8f),
                Pair(verifyingFileIntegrity, 1.0f)
            )

            // Start the actual scan
            onStartScan()

            scanningSteps.forEach { (text, targetProgress) ->
                scanningText = text
                progress = targetProgress
                delay(400) // 400ms per step
            }

            scanningText = scanAlmostComplete
            delay(500) // Brief pause before completing
            onScanComplete()
        }
    }
    
    // Handle actual scan completion
    LaunchedEffect(isActuallyScanning, scanProgress) {
        if (isActuallyScanning && scanProgress >= 1.0f) {
            // Actual scan completed, don't call onScanComplete here as it's handled by ViewModel
            scanningText = scanAlmostComplete
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // Scan button/animation area

        Spacer(modifier = Modifier.height(60.dp))

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(1f)
                .clickable(
                    enabled = !isScanning && !isActuallyScanning,
                    onClick = {
                        if (!isScanning && !isActuallyScanning) {
                            isScanning = true
                            progress = 0f
                        }
                    }
                ),
            contentAlignment = Alignment.Center
        ) {
            if (isScanning || isActuallyScanning) {
                // Show Lottie animation when scanning
                LottieAnimation(
                    composition = composition,
                    iterations = LottieConstants.IterateForever,
                    modifier = Modifier.fillMaxSize()
                )
            } else {
                // Show scan button when not scanning
                Image(
                    painter = painterResource(id = R.mipmap.img_scan_btn),
                    contentDescription = stringResource(R.string.start_scan),
                    modifier = Modifier.fillMaxSize()
                )
            }
        }
        
        Spacer(modifier = Modifier.height(40.dp))
        
        Text(
            text = when {
                isActuallyScanning && actualScanPath.isNotEmpty() -> actualScanPath
                isScanning || isActuallyScanning -> scanningText.ifEmpty { stringResource(R.string.initializing_scan) }
                else -> {
                    when (scanType) {
                        ScanType.PHOTO -> stringResource(R.string.tap_to_scan_photos)
                        ScanType.VIDEO -> stringResource(R.string.tap_to_scan_videos)
                        ScanType.AUDIO -> stringResource(R.string.tap_to_scan_audios)
                        ScanType.DOCUMENT -> stringResource(R.string.tap_to_scan) // OTHER 类型保持原文案
                        ScanType.ALL -> stringResource(R.string.tap_to_scan)
                    }
                }
            },
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = colorResource(R.color.first_text_black),
            textAlign = TextAlign.Center
        )
    }
}