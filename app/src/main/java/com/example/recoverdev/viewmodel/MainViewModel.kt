package com.example.recoverdev.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.recoverdev.data.model.FileType
import com.example.recoverdev.data.model.RecoverableFile
import com.example.recoverdev.data.model.ScanResult
import com.example.recoverdev.data.repository.InMemoryFileRepository
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class MainViewModel(
    private val repository: InMemoryFileRepository
) : ViewModel() {
    
    private val _scanningState = MutableStateFlow<ScanningState>(ScanningState.Idle)
    val scanningState: StateFlow<ScanningState> = _scanningState.asStateFlow()
    
    private val _scannedFiles = MutableStateFlow<List<RecoverableFile>>(emptyList())
    val scannedFiles: StateFlow<List<RecoverableFile>> = _scannedFiles.asStateFlow()
    
    private val _scanResult = MutableStateFlow<ScanResult?>(null)
    val scanResult: StateFlow<ScanResult?> = _scanResult.asStateFlow()
    
    private val _currentScanPath = MutableStateFlow("")
    val currentScanPath: StateFlow<String> = _currentScanPath.asStateFlow()
    
    private val _scanProgress = MutableStateFlow(0f)
    val scanProgress: StateFlow<Float> = _scanProgress.asStateFlow()
    
    private var currentFileType: FileType? = null
    private var currentScanJob: kotlinx.coroutines.Job? = null
    
    fun startScan(fileType: FileType) {
        if (_scanningState.value is ScanningState.Scanning) {
            Log.d("MainViewModel", "Scan already in progress, ignoring duplicate request")
            return
        }

        // Cancel any existing scan job
        currentScanJob?.cancel()
        currentScanJob = null

        // Clear previous scan data
        _scannedFiles.value = emptyList()
        _scanResult.value = null
        _scanProgress.value = 0f
        _currentScanPath.value = ""

        // Set new scan state
        currentFileType = fileType
        _scanningState.value = ScanningState.ReadyToScan(fileType)
    }
    
    fun startActualScan() {
        currentFileType?.let { fileType ->
            performScan(fileType)
        }
    }
    
    fun retryScan() {
        currentFileType?.let { fileType ->
            performScan(fileType)
        }
    }
    
    private fun performScan(fileType: FileType) {
        currentScanJob?.cancel()

        currentScanJob = viewModelScope.launch {
            try {
                Log.d("MainViewModel", "Starting scan: $fileType")
                _scanningState.value = ScanningState.Scanning(fileType)
                _scanProgress.value = 0f
                _currentScanPath.value = ""

                val startTime = System.currentTimeMillis()

                Log.d("MainViewModel", "Starting to fetch files")
                val scanResult = repository.scanForFilesWithCategories(fileType)

                val scanDuration = System.currentTimeMillis() - startTime
                Log.d("MainViewModel", "Actual scan duration: ${scanDuration}ms")

                val minDisplayTime = 3000L
                if (scanDuration < minDisplayTime) {
                    val remainingTime = minDisplayTime - scanDuration
                    Log.d("MainViewModel", "Scan time less than 3 seconds, waiting for an additional: ${remainingTime}ms")
                    delay(remainingTime)
                }

                _scanProgress.value = 1.0f
                _scannedFiles.value = scanResult.allFiles
                _scanResult.value = scanResult
                Log.d("MainViewModel", "Scan completed, found ${scanResult.totalFiles} files, categorized into ${scanResult.folderCategories.size} folders")
                _scanningState.value = ScanningState.Completed(fileType)
            } catch (e: Exception) {
                Log.e("MainViewModel", "Scan failed", e)
                _scanningState.value = ScanningState.Error(e.message ?: "Scan failed, please retry")
            }
        }
    }
    
    fun markNavigatedToResults() {
        currentFileType?.let { fileType ->
            Log.d("MainViewModel", "Marked as navigated to results page")
            _scanningState.value = ScanningState.NavigatedToResults(fileType)
        }
    }
    
    fun updateCurrentScanPath(path: String) {
        _currentScanPath.value = path
        // Update progress based on scan path changes
        // This is a simple progress estimation based on path updates
        val currentProgress = _scanProgress.value
        if (currentProgress < 0.9f) {
            _scanProgress.value = (currentProgress + 0.1f).coerceAtMost(0.9f)
        }
    }
    
    fun resetScanningState() {
        Log.d("MainViewModel", "Resetting scan state")
        
        currentScanJob?.cancel()
        currentScanJob = null
        
        _scanningState.value = ScanningState.Idle
        _scannedFiles.value = emptyList()
        _scanResult.value = null
        _scanProgress.value = 0f
        _currentScanPath.value = ""
        currentFileType = null
        repository.clearScanCache()
    }
    fun removeFileFromScanResult(fileId: String) {
        _scanResult.value?.let { currentResult ->
            val updatedAllFiles = currentResult.allFiles.filter { it.id != fileId }
            
            val updatedFolderCategories = currentResult.folderCategories.map { category ->
                val updatedFiles = category.files.filter { it.id != fileId }
                category.copy(
                    files = updatedFiles,
                    fileCount = updatedFiles.size
                )
            }.filter { it.fileCount > 0 }
            
            val updatedScanResult = currentResult.copy(
                allFiles = updatedAllFiles,
                folderCategories = updatedFolderCategories,
                totalFiles = updatedAllFiles.size
            )
            
            _scanResult.value = updatedScanResult
            _scannedFiles.value = updatedAllFiles
            
            Log.d("MainViewModel", "Removed file $fileId from scan result. Remaining files: ${updatedAllFiles.size}")
        }
    }

    fun removeFilesFromScanResult(fileIds: Set<String>) {
        _scanResult.value?.let { currentResult ->
            val updatedAllFiles = currentResult.allFiles.filter { it.id !in fileIds }
            
            val updatedFolderCategories = currentResult.folderCategories.map { category ->
                val updatedFiles = category.files.filter { it.id !in fileIds }
                category.copy(
                    files = updatedFiles,
                    fileCount = updatedFiles.size
                )
            }.filter { it.fileCount > 0 }
            
            val updatedScanResult = currentResult.copy(
                allFiles = updatedAllFiles,
                folderCategories = updatedFolderCategories,
                totalFiles = updatedAllFiles.size
            )
            
            _scanResult.value = updatedScanResult
            _scannedFiles.value = updatedAllFiles
            
            Log.d("MainViewModel", "Removed ${fileIds.size} files from scan result. Remaining files: ${updatedAllFiles.size}")
        }
    }
}

sealed class ScanningState {
    object Idle : ScanningState()
    data class ReadyToScan(val fileType: FileType) : ScanningState()
    data class Scanning(val fileType: FileType) : ScanningState()
    data class Completed(val fileType: FileType) : ScanningState()
    data class Error(val message: String) : ScanningState()
    data class NavigatedToResults(val fileType: FileType) : ScanningState()
}