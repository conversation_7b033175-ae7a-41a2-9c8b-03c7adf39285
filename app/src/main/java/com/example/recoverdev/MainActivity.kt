package com.example.recoverdev

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.compose.BackHandler
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.border
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import com.example.recoverdev.data.model.FileType
import com.example.recoverdev.data.repository.InMemoryFileRepository
import com.example.recoverdev.recovery.FileRecoveryEngine
import com.example.recoverdev.ui.screen.*
import com.example.recoverdev.ui.components.DeleteConfirmationDialog
import com.example.recoverdev.viewmodel.SettingsViewModel
import com.example.recoverdev.ui.theme.RecoverDevTheme
import com.example.recoverdev.utils.PermissionUtils
import com.example.recoverdev.viewmodel.MainViewModel
import com.example.recoverdev.viewmodel.RecoveredDataViewModel
import com.example.recoverdev.viewmodel.RecoveryViewModel
import com.example.recoverdev.viewmodel.ScanningState
import com.example.recoverdev.ui.components.AppTopBar
import com.example.recoverdev.ui.components.HomeTopBar
import com.example.recoverdev.ui.components.AllFilesAccessDialog
import com.example.recoverdev.ui.components.ExitConfirmationDialog
import com.example.recoverdev.utils.PreferencesUtils
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class MainActivity : ComponentActivity() {
    
    private var hasPermissions by mutableStateOf(false)
    private var showAllFilesAccessDialog by mutableStateOf(false)
    private var showExitDialog by mutableStateOf(false)
    private var pendingFileType: FileType? = null
    
    // Manage external storage permission launcher
    private val manageExternalStorageLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { _ ->
        val newPermissionStatus = PermissionUtils.hasManageExternalStoragePermission(this)
        hasPermissions = newPermissionStatus
        
        if (newPermissionStatus && pendingFileType != null) {
        }
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        val recoveryEngine = FileRecoveryEngine(this)
        val repository = InMemoryFileRepository(recoveryEngine, this)
        
        setContent {
            RecoverDevTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    RecoveryApp(
                        repository = repository,
                        onPermissionRequired = { 
                            if (PermissionUtils.needsManageExternalStoragePermission()) {
                                requestManageExternalStoragePermission()
                            }
                        },
                        onShowPermissionDialog = { fileType ->
                            pendingFileType = fileType
                            showAllFilesAccessDialog = true
                        },
                        pendingFileType = pendingFileType,
                        onClearPendingFileType = { pendingFileType = null },
                        hasPermissions = hasPermissions,
                        onShowExitDialog = { showExitDialog = true }
                    )
                    
                    // Show All Files Access dialog
                    AllFilesAccessDialog(
                        showDialog = showAllFilesAccessDialog,
                        onDismiss = { showAllFilesAccessDialog = false },
                        onAllow = {
                            showAllFilesAccessDialog = false
                            PreferencesUtils.setNotificationPermissionRequested(this@MainActivity)
                            if (PermissionUtils.needsManageExternalStoragePermission()) {
                                requestManageExternalStoragePermission()
                            } else {
                                hasPermissions = true
                            }
                        }
                    )

                    val scope = rememberCoroutineScope()
                    // Show Exit Confirmation dialog
                    ExitConfirmationDialog(
                        showDialog = showExitDialog,
                        onDismiss = { showExitDialog = false },
                        onExit = {
                            scope.launch {
                                showExitDialog = false
                                delay(200)
                                moveTaskToBack(false)
                            }
                        }
                    )
                }
            }
        }
        
        if (PreferencesUtils.isFirstLaunch(this)) {
            showAllFilesAccessDialog = true
            PreferencesUtils.setFirstLaunchCompleted(this)
        }
    }
    
    private fun requestManageExternalStoragePermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            try {
                val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
                intent.data = Uri.parse("package:$packageName")
                manageExternalStorageLauncher.launch(intent)
            } catch (e: Exception) {
                // If unable to open settings page, try general settings
                val intent = Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION)
                manageExternalStorageLauncher.launch(intent)
            }
        }
    }

}

@Composable
fun RecoveryApp(
    repository: InMemoryFileRepository,
    onPermissionRequired: () -> Unit,
    onShowPermissionDialog: (FileType) -> Unit,
    pendingFileType: FileType?,
    onClearPendingFileType: () -> Unit,
    hasPermissions: Boolean,
    onShowExitDialog: () -> Unit
) {
    val navController = rememberNavController()
    val context = LocalContext.current
    
    // Create ViewModels (simplified version, should use Hilt or other DI framework in real projects)
    val mainViewModel = remember { MainViewModel(repository) }
    val recoveryViewModel = remember { RecoveryViewModel(repository) }
    val recoveredDataViewModel = remember { RecoveredDataViewModel(repository) }
    
    // Set up scan path update callback
    LaunchedEffect(Unit) {
        repository.setOnScanPathUpdate { path ->
            mainViewModel.updateCurrentScanPath(path)
        }
    }
    
    val scanningState by mainViewModel.scanningState.collectAsState()
    val scannedFiles by mainViewModel.scannedFiles.collectAsState()
    val scanResult by mainViewModel.scanResult.collectAsState()
    val currentScanPath by mainViewModel.currentScanPath.collectAsState()
    val scanProgress by mainViewModel.scanProgress.collectAsState()
    
    val currentDestination = navController.currentBackStackEntryAsState().value?.destination?.route
    val currentFileId = navController.currentBackStackEntryAsState().value?.arguments?.getString("fileId")
    val isCurrentlyScanning = currentDestination?.contains("scanning") == true &&
        (scanningState is ScanningState.Scanning || scanningState is ScanningState.ReadyToScan)

    val canNavigateBack = navController.previousBackStackEntry != null && !isCurrentlyScanning
    
    val topBarTitle = when {
        currentDestination == "main" -> stringResource(R.string.app_name)
        currentDestination == "settings" -> stringResource(R.string.settings)
        currentDestination == "info" -> stringResource(R.string.file_recovery_information)
        currentDestination == "recovered_data" -> stringResource(R.string.recovered_data)
        currentDestination?.startsWith("folder_category") == true -> {
            when (scanResult?.fileType) {
                FileType.PHOTO -> stringResource(R.string.photos)
                FileType.VIDEO -> stringResource(R.string.videos)
                FileType.AUDIO -> stringResource(R.string.audios)
                FileType.OTHER -> stringResource(R.string.other_files_type)
                else -> stringResource(R.string.app_name)
            }
        }
        currentDestination?.startsWith("recovery") == true -> {
            val folderName by recoveryViewModel.folderName.collectAsState()
            folderName ?: when (scanResult?.fileType) {
                FileType.PHOTO -> stringResource(R.string.photos)
                FileType.VIDEO -> stringResource(R.string.videos)
                FileType.AUDIO -> stringResource(R.string.audios)
                FileType.OTHER -> stringResource(R.string.other_files)
                else -> stringResource(R.string.file_recovery)
            }
        }
        currentDestination?.startsWith("file_detail") == true -> stringResource(R.string.file_details)
        currentDestination?.contains("scanning") == true -> {
            when (val state = scanningState) {
                is ScanningState.ReadyToScan -> {
                    when (state.fileType) {
                        FileType.PHOTO -> stringResource(R.string.recovery_photo)
                        FileType.VIDEO -> stringResource(R.string.recover_video)
                        FileType.AUDIO -> stringResource(R.string.recover_audio)
                        FileType.OTHER -> stringResource(R.string.recover_other_files)
                    }
                }
                is ScanningState.Scanning -> {
                    when (state.fileType) {
                        FileType.PHOTO -> stringResource(R.string.recovery_photo)
                        FileType.VIDEO -> stringResource(R.string.recover_video)
                        FileType.AUDIO -> stringResource(R.string.recover_audio)
                        FileType.OTHER -> stringResource(R.string.recover_other_files)
                    }
                }
                is ScanningState.Completed -> {
                    when (state.fileType) {
                        FileType.PHOTO -> stringResource(R.string.recovery_photo)
                        FileType.VIDEO -> stringResource(R.string.recover_video)
                        FileType.AUDIO -> stringResource(R.string.recover_audio)
                        FileType.OTHER -> stringResource(R.string.recover_other_files)
                    }
                }
                else -> stringResource(R.string.file_recovery)
            }
        }
        else -> stringResource(R.string.scan_completed)
    }
    
    Scaffold(
        topBar = {
            if (currentDestination == "main") {
                HomeTopBar(
                    onSettingsClick = {
                        navController.navigate("settings")
                    }
                )
            } else {
                AppTopBar(
                    title = topBarTitle,
                    showBackButton = canNavigateBack,
                    onBackClick = if (canNavigateBack) {
                        {
                            when {
                                currentDestination?.startsWith("recovery/") == true -> {
                                    val isSelectionMode = recoveryViewModel.isSelectionMode.value
                                    if (isSelectionMode) {
                                        recoveryViewModel.exitSelectionMode()
                                    } else {
                                        navController.popBackStack()
                                    }
                                }
                                currentDestination == "scanning" -> {
                                    val scanningState = mainViewModel.scanningState.value
                                    if (scanningState is ScanningState.Completed || scanningState is ScanningState.NavigatedToResults) {
                                        navController.navigate("main") {
                                            popUpTo("main") { inclusive = true }
                                        }
                                    } else if (scanningState is ScanningState.Error || scanningState is ScanningState.Idle) {
                                        mainViewModel.resetScanningState()
                                        navController.navigate("main") {
                                            popUpTo("main") { inclusive = true }
                                        }
                                    }
                                }
                                currentDestination?.startsWith("scan_result/") == true -> {
                                    navController.navigate("main") {
                                        popUpTo("main") { inclusive = true }
                                    }
                                }
                                else -> {
                                    navController.popBackStack()
                                }
                            }
                        }
                    } else null,
                    actions = {
                        when {
                            currentDestination?.startsWith("recovery/") == true -> {
                                val isSelectionMode by recoveryViewModel.isSelectionMode.collectAsState()
                                val isAllSelected by remember(recoveryViewModel.selectedFiles.collectAsState().value, recoveryViewModel.files.collectAsState().value) {
                                    derivedStateOf { recoveryViewModel.isAllFilesSelected() }
                                }
                                val selectedFiles by recoveryViewModel.selectedFiles.collectAsState()
                                
                                if (!isSelectionMode) {
                                    Box(
                                        modifier = Modifier.padding(horizontal = 12.dp)
                                            .background(color = colorResource(R.color.btn_orange), RoundedCornerShape(16.dp))
                                            .padding(vertical = 6.dp, horizontal = 12.dp)
                                            .clickable { recoveryViewModel.toggleSelectionMode() }
                                    ) {
                                        Text(
                                            text = stringResource(R.string.select),
                                            fontSize = 12.sp,
                                            lineHeight = 12.sp,
                                            fontWeight = FontWeight.Medium,
                                            color = Color.White
                                        )
                                    }
                                } else {
                                    Box(
                                        modifier = Modifier
                                            .padding(2.dp)
                                            .clickable { recoveryViewModel.selectAllFiles() },
                                    ) {
                                        if (isAllSelected) {
                                            Icon(
                                                imageVector = Icons.Default.Check,
                                                contentDescription = "All Selected",
                                                tint = Color.White,
                                                modifier = Modifier
                                                    .size(20.dp)
                                                    .background(color = colorResource(R.color.btn_orange), RoundedCornerShape(4.dp))
                                            )
                                        } else {
                                            Box(
                                                modifier = Modifier
                                                    .size(20.dp)
                                                    .background(Color(0xFFFFE0C1), RoundedCornerShape(4.dp))
                                                    .border(
                                                        width = 2.dp,
                                                        color = colorResource(R.color.btn_orange),
                                                        shape = RoundedCornerShape(4.dp)
                                                    )
                                            )
                                        }
                                    }
                                    
                                    Spacer(modifier = Modifier.width(12.dp))

                                    Image(
                                        painter = painterResource(id = R.mipmap.nav_del),
                                        contentDescription = "Delete Selected",
                                        modifier = Modifier.size(25.dp)
                                            .clickable {
                                                if (selectedFiles.isEmpty()) {
                                                    recoveryViewModel.showNoSelectionDialog()
                                                } else {
                                                    recoveryViewModel.showDeleteConfirmationDialog()
                                                }
                                            }
                                    )

                                    Spacer(modifier = Modifier.width(12.dp))
                                }
                            }
                            currentDestination?.startsWith("file_detail/") == true -> {
                                val recoveryFiles by recoveryViewModel.files.collectAsState()
                                
                                IconButton(
                                    onClick = {
                                        currentFileId?.let { fileId ->
                                            val file = recoveryFiles.find { it.id == fileId } ?: scannedFiles.find { it.id == fileId }
                                            file?.let {
                                                recoveryViewModel.showDeleteConfirmationDialog()
                                            }
                                        }
                                    }
                                ) {
                                    Image(
                                        painter = painterResource(id = R.mipmap.nav_del),
                                        contentDescription = "Delete File",
                                        modifier = Modifier.size(24.dp)
                                    )
                                }
                            }
                            currentDestination?.startsWith("recovered_file_detail/") == true -> {
                                val recoveredFiles by recoveredDataViewModel.recoveredFiles.collectAsState()
                                
                                IconButton(
                                    onClick = {
                                        currentFileId?.let { fileId ->
                                            val file = recoveredFiles.find { it.id == fileId }
                                            file?.let {
                                                recoveredDataViewModel.showDeleteConfirmationDialog()
                                            }
                                        }
                                    }
                                ) {
                                    Image(
                                        painter = painterResource(id = R.mipmap.nav_del),
                                        contentDescription = "Delete File",
                                        modifier = Modifier.size(24.dp)
                                    )
                                }
                            }
                            currentDestination == "settings" -> {}
                            else -> {}
                        }
                    }
                )
            }
        },
        containerColor = colorResource(R.color.main_background)
    ) { paddingValues ->
    
        NavHost(
            navController = navController,
            startDestination = "main",
            modifier = Modifier.padding(paddingValues),
            enterTransition = { EnterTransition.None },
            exitTransition = { ExitTransition.None },
            popEnterTransition = { EnterTransition.None },
            popExitTransition = { ExitTransition.None }
        ) {
            composable("main") {
                // Handle back press on main screen to show exit dialog
                BackHandler {
                    onShowExitDialog()
                }
                
                MainScreenContent(
                    onRecoveryClick = { fileType ->
                        val hasManageExternalStoragePermission = if (PermissionUtils.needsManageExternalStoragePermission()) {
                            PermissionUtils.hasManageExternalStoragePermission(context)
                        } else {
                            true
                        }
                        
                        if (!hasManageExternalStoragePermission) {
                            onShowPermissionDialog(fileType)
                        } else {
                            mainViewModel.resetScanningState()
                            mainViewModel.startScan(fileType)
                            navController.navigate("scanning")
                        }
                    },
                    onRecoveredDataClick = {
                        navController.navigate("recovered_data")
                    },
                    onInfoClick = {
                        navController.navigate("info")
                    }
                )
                
                LaunchedEffect(pendingFileType, hasPermissions) {
                    pendingFileType?.let { fileType ->
                        val hasPermission = if (PermissionUtils.needsManageExternalStoragePermission()) {
                            PermissionUtils.hasManageExternalStoragePermission(context)
                        } else {
                            true
                        }
                        
                        if (hasPermission && hasPermissions) {
                            onClearPendingFileType()
                            mainViewModel.resetScanningState()
                            mainViewModel.startScan(fileType)
                            if (currentDestination != "scanning") {
                                navController.navigate("scanning")
                            }
                        }
                    }
                }
            }
        
            composable("scanning") {
                when (val state = scanningState) {
                    is ScanningState.ReadyToScan -> {
                        ScanningScreen(
                            scanType = fileTypeToScanType(state.fileType),
                            onScanComplete = {
                            },
                            onStartScan = {
                                mainViewModel.startActualScan()
                            },
                            currentScanPath = "",
                            scanProgress = 0f,
                            isActuallyScanning = false
                        )
                    }
                    is ScanningState.Scanning -> {
                        ScanningScreen(
                            scanType = fileTypeToScanType(state.fileType),
                            onScanComplete = {
                            },
                            onStartScan = {
                            },
                            currentScanPath = currentScanPath,
                            scanProgress = scanProgress,
                            isActuallyScanning = true
                        )
                    }
                    is ScanningState.Completed -> {
                        // Immediately navigate to results when scan is completed
                        LaunchedEffect(Unit) {
                            navController.navigate("scan_result/${state.fileType.name}") {
                                popUpTo("scanning") { inclusive = true }
                                launchSingleTop = true
                            }
                        }
                        // Show empty box while navigating
                        Box(modifier = Modifier.fillMaxSize())
                    }
                    is ScanningState.NavigatedToResults -> {
                        LaunchedEffect(Unit) {
                            navController.navigate("main") {
                                popUpTo("main") { inclusive = true }
                            }
                        }
                    }
                    is ScanningState.Error -> {
                        // Show error message
                        ErrorScreenContent(
                            message = state.message,
                            onRetryClick = { 
                                // Restart scanning
                                mainViewModel.retryScan()
                            }
                        )
                    }
                    is ScanningState.Idle -> {
                        LaunchedEffect(Unit) {
                            navController.navigate("main") {
                                popUpTo("main") { inclusive = true }
                            }
                        }
                    }
                }
            }
            
            composable("scan_result/{fileType}") { backStackEntry ->
                val fileTypeString = backStackEntry.arguments?.getString("fileType") ?: "PHOTO"
                val fileType = FileType.valueOf(fileTypeString)
                
                ScanResultScreenContent(
                    fileType = fileType,
                    foundCount = scannedFiles.size,
                    onViewFiles = {
                        mainViewModel.markNavigatedToResults()
                        navController.navigate("folder_category/${fileType.name}") {
                            popUpTo("main") { inclusive = false }
                        }
                    }
                )
            }
        
            composable("folder_category/{fileType}") { backStackEntry ->
                val fileTypeString = backStackEntry.arguments?.getString("fileType") ?: "PHOTO"
                val fileType = FileType.valueOf(fileTypeString)
                
                scanResult?.let { result ->
                    FolderCategoryScreenContent(
                        scanResult = result,
                        onFolderClick = { category ->
                            recoveryViewModel.setFilesWithFolder(category.files, category.name)
                            navController.navigate("recovery/${fileType.name}")
                        },
                        onAllFilesClick = { allFiles ->
                            recoveryViewModel.setFiles(allFiles)
                            recoveryViewModel.clearFolderName()
                            navController.navigate("recovery/${fileType.name}")
                        },
                        onFileClick = { file ->
                            navController.navigate("file_detail/${file.id}")
                        }
                    )
                }
            }
        
            composable("recovery/{fileType}") { backStackEntry ->
                val fileTypeString = backStackEntry.arguments?.getString("fileType") ?: "PHOTO"
                val fileType = FileType.valueOf(fileTypeString)
                
                val recoveryFiles by recoveryViewModel.files.collectAsState()

                RecoveryScreenContent(
                    fileType = fileType,
                    files = recoveryFiles, // Use files from RecoveryViewModel
                    onFileClick = { file ->
                        navController.navigate("file_detail/${file.id}")
                    },
                    onRecoverClick = {
                        navController.navigate("recovery_animation_batch")
                    },
                    onBackClick = {
                        navController.popBackStack()
                    },
                    viewModel = recoveryViewModel,
                    onFilesDeleted = { deletedFileIds ->
                        mainViewModel.removeFilesFromScanResult(deletedFileIds)
                    }
                )
            }
        
            composable("file_detail/{fileId}") { backStackEntry ->
                val fileId = backStackEntry.arguments?.getString("fileId") ?: ""
                val recoveryFiles by recoveryViewModel.files.collectAsState()
                val folderName by recoveryViewModel.folderName.collectAsState()
                val showDeleteDialog by recoveryViewModel.showDeleteDialog.collectAsState()
                val file = recoveryFiles.find { it.id == fileId } ?: scannedFiles.find { it.id == fileId }
                
                if (file != null) {
                    DeleteConfirmationDialog(
                        showDialog = showDeleteDialog,
                        onDismiss = { 
                            recoveryViewModel.hideDeleteConfirmationDialog() 
                        },
                        onConfirm = {
                            recoveryViewModel.deleteSingleFile(file)
                            mainViewModel.removeFileFromScanResult(file.id)
                            recoveryViewModel.hideDeleteConfirmationDialog()
                            navController.popBackStack()
                        }
                    )
                    
                    FileDetailScreenContent(
                        file = file,
                        folderName = folderName,
                        isRecovered = file.isRecovered,
                        onRecoverClick = {
                            navController.navigate("recovery_animation/${file.id}")
                        },
                        onDeleteClick = {
                        }
                    )
                }
            }
        
        composable("recovery_animation/{fileId}") { backStackEntry ->
            val fileId = backStackEntry.arguments?.getString("fileId") ?: ""
            val file = scannedFiles.find { it.id == fileId }
            
            RecoveryAnimationScreen(
                fileType = file?.type,
                onAnimationComplete = {
                    file?.let { recoveryViewModel.recoverSingleFile(it) }
                    navController.navigate("recovery_result") {
                        popUpTo("recovery_animation/${fileId}") { inclusive = true }
                    }
                }
            )
        }
        
        composable("recovery_animation_batch") {
            val recoveryFiles by recoveryViewModel.files.collectAsState()
            val selectedFileIds by recoveryViewModel.selectedFiles.collectAsState()
            val selectedFiles = recoveryFiles.filter { it.id in selectedFileIds }
            
            // Determine the most common file type among selected files
            val fileType = selectedFiles.groupBy { it.type }
                .maxByOrNull { it.value.size }?.key
            
            RecoveryAnimationScreen(
                fileType = fileType,
                onAnimationComplete = {
                    recoveryViewModel.recoverSelectedFiles()
                    navController.navigate("recovery_result") {
                        popUpTo("recovery_animation_batch") { inclusive = true }
                    }
                }
            )
        }
        
        composable("recovery_result") {
            RecoveryResultScreen(
                onOkClick = {
                    navController.navigate("main") {
                        popUpTo("main") { inclusive = true }
                    }
                }
            )
        }
        
            composable("recovered_data") {
                RecoveredDataScreenContent(
                    onFileClick = { file ->
                        navController.navigate("recovered_file_detail/${file.id}")
                    },
                    viewModel = recoveredDataViewModel
                )
            }
        
        composable("recovered_folder_files/{folderId}") { backStackEntry ->
            val recoveryFiles by recoveryViewModel.files.collectAsState()

            RecoveryScreen(
                fileType = FileType.OTHER,
                files = recoveryFiles,
                onBackClick = {
                    navController.popBackStack()
                },
                onFileClick = { file ->
                    navController.navigate("recovered_file_detail/${file.id}")
                },
                onRecoverClick = {
                },
                viewModel = recoveryViewModel
            )
        }
        
        composable("recovered_file_detail/{fileId}") { backStackEntry ->
            val fileId = backStackEntry.arguments?.getString("fileId") ?: ""
            val recoveredFiles by recoveredDataViewModel.recoveredFiles.collectAsState()
            val showDeleteDialog by recoveredDataViewModel.showDeleteDialog.collectAsState()
            val file = recoveredFiles.find { it.id == fileId }
            
            if (file != null) {
                DeleteConfirmationDialog(
                    showDialog = showDeleteDialog,
                    onDismiss = { 
                        recoveredDataViewModel.hideDeleteConfirmationDialog() 
                    },
                    onConfirm = {
                        recoveredDataViewModel.deleteFile(file)
                        recoveredDataViewModel.hideDeleteConfirmationDialog()
                        navController.popBackStack()
                    }
                )
                
                FileDetailScreen(
                    file = file,
                    folderName = null,
                    isRecovered = true,
                    onBackClick = {
                        navController.popBackStack()
                    },
                    onRecoverClick = {
                    },
                    onDeleteClick = {
                    }
                )
            }
        }
        
            composable("settings") {
                val context = LocalContext.current
                val settingsViewModel = remember { SettingsViewModel(context) }
                val currentLanguage by settingsViewModel.currentLanguage.collectAsState()
                
                SettingsScreenContent(
                    currentLanguage = currentLanguage,
                    onLanguageChanged = { language ->
                        settingsViewModel.applyLanguage(language)
                        (context as? ComponentActivity)?.recreate()
                    }
                )
            }
        
            composable("info") {
                InfoScreenContent()
            }
        }
    }
    
    LaunchedEffect(scanningState) {
        when (scanningState) {
            is ScanningState.ReadyToScan, is ScanningState.Scanning -> {
                navController.navigate("scanning") {
                    popUpTo("main") { inclusive = false }
                }
            }
            else -> {}
        }
    }
}